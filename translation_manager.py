import requests
import json
import time
from typing import Optional, Dict, Any
from threading import Lock

class TranslationManager:
    """Manages translation of text to Japanese using Google Translate API"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.translation_config = config.get("translation", {})
        self.enabled = self.translation_config.get("enabled", True)
        self.target_language = self.translation_config.get("target_language", "ja")  # Japanese
        self.source_language = self.translation_config.get("source_language", "auto")  # Auto-detect
        
        # Cache for translations to avoid repeated API calls
        self.translation_cache: Dict[str, str] = {}
        self.max_cache_size = self.translation_config.get("max_cache_size", 100)
        self.cache_lock = Lock()
        
        # API configuration
        self.api_timeout = self.translation_config.get("api_timeout", 10)
        self.retry_attempts = self.translation_config.get("retry_attempts", 2)
        
        print(f"✓ Translation Manager initialized - Target: {self.target_language}")
    
    def translate_text(self, text: str, target_lang: Optional[str] = None) -> Optional[str]:
        """
        Translate text to target language using Google Translate API
        
        Args:
            text: Text to translate
            target_lang: Target language code (default: self.target_language)
            
        Returns:
            Translated text or None if translation fails
        """
        if not self.enabled or not text.strip():
            return None
            
        if target_lang is None:
            target_lang = self.target_language
        
        # Check cache first
        cache_key = f"{text}_{target_lang}"
        with self.cache_lock:
            if cache_key in self.translation_cache:
               # print(f"🚀 Using cached translation")
                return self.translation_cache[cache_key]
        
        # Try multiple translation methods
        translated_text = None
        
        # Method 1: Try Google Translate API (free endpoint)
        translated_text = self._translate_with_google_free(text, target_lang)
        
        # Method 2: Fallback to MyMemory API if Google fails
        if not translated_text:
            translated_text = self._translate_with_mymemory(text, target_lang)
        
        # Method 3: Fallback to LibreTranslate if available
        if not translated_text:
            translated_text = self._translate_with_libretranslate(text, target_lang)
        
        # Cache the result if successful
        if translated_text:
            with self.cache_lock:
                # Manage cache size
                if len(self.translation_cache) >= self.max_cache_size:
                    oldest_key = next(iter(self.translation_cache))
                    del self.translation_cache[oldest_key]
                
                self.translation_cache[cache_key] = translated_text
                print(f"✓ Cached translation: {text[:30]}... -> {translated_text[:30]}...")
        
        return translated_text
    
    def _translate_with_google_free(self, text: str, target_lang: str) -> Optional[str]:
        """Translate using Google Translate free endpoint"""
        try:
            # Using Google Translate's free endpoint
            url = "https://translate.googleapis.com/translate_a/single"
            params = {
                'client': 'gtx',
                'sl': self.source_language,
                'tl': target_lang,
                'dt': 't',
                'q': text
            }
            
            response = requests.get(url, params=params, timeout=self.api_timeout)
            
            if response.status_code == 200:
                result = response.json()
                if result and len(result) > 0 and len(result[0]) > 0:
                    translated_text = result[0][0][0]
                   # print(f"✓ Google Translate: {text[:30]}... -> {translated_text[:30]}...")
                    return translated_text
                    
        except Exception as e:
            print(f"Google Translate failed: {e}")
        
        return None
    
    def _translate_with_mymemory(self, text: str, target_lang: str) -> Optional[str]:
        """Translate using MyMemory API as fallback"""
        try:
            url = "https://api.mymemory.translated.net/get"
            params = {
                'q': text,
                'langpair': f"{self.source_language}|{target_lang}"
            }
            
            response = requests.get(url, params=params, timeout=self.api_timeout)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('responseStatus') == 200:
                    translated_text = result['responseData']['translatedText']
                   # print(f"✓ MyMemory Translate: {text[:30]}... -> {translated_text[:30]}...")
                    return translated_text
                    
        except Exception as e:
            print(f"MyMemory Translate failed: {e}")
        
        return None
    
    def _translate_with_libretranslate(self, text: str, target_lang: str) -> Optional[str]:
        """Translate using LibreTranslate API as fallback"""
        try:
            # This would require a LibreTranslate instance
            # For now, return None as it's not commonly available
            return None
            
        except Exception as e:
            print(f"LibreTranslate failed: {e}")
        
        return None
    
    def format_bilingual_response(self, original_text: str, translated_text: str) -> str:
        """
        Format the response to show both Japanese and original language
        
        Args:
            original_text: Original response text
            translated_text: Japanese translation
            
        Returns:
            Formatted bilingual response
        """
        if not translated_text:
            return original_text
        
        # Format as requested: Jp: [Japanese] En: [Original]
        return f"JP: {translated_text}\nID: {original_text}"
    
    def get_japanese_text(self, bilingual_text: str) -> str:
        """
        Extract Japanese text from bilingual response for audio output
        
        Args:
            bilingual_text: Formatted bilingual text
            
        Returns:
            Japanese text only
        """
        lines = bilingual_text.split('\n')
        for line in lines:
            if line.startswith('Jp: '):
                return line[4:]  # Remove "Jp: " prefix
        
        # Fallback: return original text
        return bilingual_text
    
    def clear_cache(self):
        """Clear the translation cache"""
        with self.cache_lock:
            self.translation_cache.clear()
          #  print("✓ Translation cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get translation cache statistics"""
        with self.cache_lock:
            return {
                "cache_enabled": True,
                "cache_size": len(self.translation_cache),
                "max_cache_size": self.max_cache_size,
                "cached_translations": list(self.translation_cache.keys())
            }
